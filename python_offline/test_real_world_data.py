#!/usr/bin/env python3
"""
Test script for real-world data loading functionality.

This script tests the new real-world data loading capabilities and demonstrates
how to process real-world PPG data without ground truth.
"""

import os
import sys
import numpy as np

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bspml import (
    load_real_world_data,
    get_available_real_world_files,
    detect_data_type,
    load_data_auto,
    preprocess_ppg,
    estimate_heart_rate,
    evaluate_pipeline_performance,
    print_evaluation_summary,
)


def test_real_world_data_loading():
    """Test loading real-world data files."""
    print("=" * 60)
    print("TESTING REAL-WORLD DATA LOADING")
    print("=" * 60)

    # Check for available real-world files
    real_world_path = "data/real_world"
    available_files = get_available_real_world_files(real_world_path)

    print(f"Available real-world files: {len(available_files)}")
    for file in available_files:
        print(f"  - {os.path.basename(file)}")

    if not available_files:
        print("No real-world data files found!")
        return False

    # Test loading the first available file
    test_file = available_files[0]
    print(f"\nTesting with file: {os.path.basename(test_file)}")

    try:
        # Test data type detection
        data_type = detect_data_type(test_file)
        print(f"Detected data type: {data_type}")

        # Test direct loading
        ppg_signal, acc_signals, sampling_rate, metadata = load_real_world_data(
            test_file,
            duration=30.0,  # Load 30 seconds
        )

        print(f"Loaded PPG signal: {len(ppg_signal)} samples at {sampling_rate:.2f} Hz")
        print(f"Loaded ACC signal: {acc_signals.shape} at {sampling_rate:.2f} Hz")
        print(f"Signal duration: {len(ppg_signal) / sampling_rate:.2f} seconds")
        print(f"PPG range: {np.min(ppg_signal):.0f} to {np.max(ppg_signal):.0f}")
        print(f"Metadata: {metadata}")

        return True

    except Exception as e:
        print(f"Error loading real-world data: {e}")
        return False


def test_auto_detection():
    """Test automatic data type detection and loading."""
    print("\n" + "=" * 60)
    print("TESTING AUTO-DETECTION FUNCTIONALITY")
    print("=" * 60)

    # Test with real-world file
    real_world_path = "../data/real_world"
    available_files = get_available_real_world_files(real_world_path)

    if available_files:
        test_file = available_files[0]
        print(f"Testing auto-detection with: {os.path.basename(test_file)}")

        try:
            ppg_signal, acc_signals, sampling_rate, metadata, ground_truth = (
                load_data_auto(
                    data_identifier=test_file, duration=30.0, data_path="../data"
                )
            )

            print(f"Auto-loaded PPG signal: {len(ppg_signal)} samples")
            print(
                f"Auto-loaded ACC signal: {acc_signals.shape if acc_signals is not None else 'None'}"
            )
            print(f"Sampling rate: {sampling_rate:.2f} Hz")
            print(
                f"Ground truth available: {ground_truth.get('heart_rate') is not None}"
            )
            print(f"Data source: {metadata.get('data_source', 'Unknown')}")

            return True

        except Exception as e:
            print(f"Error in auto-detection: {e}")
            return False
    else:
        print("No real-world files available for testing")
        return False


def test_pipeline_with_real_world():
    """Test the complete pipeline with real-world data."""
    print("\n" + "=" * 60)
    print("TESTING COMPLETE PIPELINE WITH REAL-WORLD DATA")
    print("=" * 60)

    real_world_path = "../data/real_world"
    available_files = get_available_real_world_files(real_world_path)

    if not available_files:
        print("No real-world files available for pipeline testing")
        return False

    test_file = available_files[0]
    print(f"Testing pipeline with: {os.path.basename(test_file)}")

    try:
        # Load data
        ppg_signal, acc_signals, sampling_rate, metadata, ground_truth = load_data_auto(
            data_identifier=test_file,
            duration=60.0,  # Process 1 minute
            data_path="../data",
        )

        print(f"Loaded {len(ppg_signal)} samples at {sampling_rate:.2f} Hz")

        # Preprocess
        print("Preprocessing...")
        ppg_processed = preprocess_ppg(
            ppg_signal=ppg_signal,
            acc_signals=acc_signals,
            sampling_rate=sampling_rate,
            enable_detrending=True,
            enable_denoising=True,
            enable_motion_removal=True,
        )

        # Estimate heart rate
        print("Estimating heart rate...")
        hr_results = estimate_heart_rate(
            ppg_signal=ppg_processed, sampling_rate=sampling_rate, return_peaks=True
        )

        if hr_results.get("success", False):
            hr_stats = hr_results.get("statistics", {})
            print(f"Heart rate estimation successful!")
            print(f"  Mean HR: {hr_stats.get('mean', 'N/A'):.1f} BPM")
            print(f"  HR Std: {hr_stats.get('std', 'N/A'):.1f} BPM")
            print(f"  Detected peaks: {hr_results.get('num_peaks', 0)}")
        else:
            print(
                f"Heart rate estimation failed: {hr_results.get('error', 'Unknown error')}"
            )

        # Evaluate (without ground truth)
        print("Evaluating performance...")
        evaluation = evaluate_pipeline_performance(
            ppg_raw=ppg_signal,
            ppg_processed=ppg_processed,
            hr_results=hr_results,
            ground_truth=ground_truth,
            sampling_rate=sampling_rate,
        )

        print_evaluation_summary(evaluation)

        return True

    except Exception as e:
        print(f"Error in pipeline testing: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Testing Real-World Data Loading Functionality")
    print("=" * 60)

    # Run tests
    test1_passed = test_real_world_data_loading()
    test2_passed = test_auto_detection()
    test3_passed = test_pipeline_with_real_world()

    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Real-world data loading: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Auto-detection: {'PASSED' if test2_passed else 'FAILED'}")
    print(f"Complete pipeline: {'PASSED' if test3_passed else 'FAILED'}")

    if all([test1_passed, test2_passed, test3_passed]):
        print("\nAll tests PASSED! ✅")
        print("Real-world data loading is working correctly.")
    else:
        print("\nSome tests FAILED! ❌")
        print("Please check the error messages above.")
